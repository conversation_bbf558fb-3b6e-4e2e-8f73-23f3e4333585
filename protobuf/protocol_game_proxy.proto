package fm.lizhi.commons.template.datacenter.protocol;

option java_package = "fm.lizhi.ocean.seal.protocol";
option java_outer_classname = "GameProxyServiceProto";

/*调用目标接口的参数*/
message InvokeTargetParams {
  required string eventName = 1; // 事件名，对应SUD的event参数
  required string channel = 2; // 渠道(sud)
  required int32 env = 3; //环境
  required string appId = 4; // appId，Seal服务端根据appId配置业务服务端域名
  required string gameId = 5; // 渠道游戏ID
  required string dataJson = 6; // 转发到渠道的参数
  optional string interface = 7; // 接口名
}

message GetAppIdAppSecretParam{
  optional string appId = 1; // appId，Seal服务端根据appId配置业务服务端域名
  optional string gameId = 2; // 游戏ID，Seal服务端根据gameId配置游戏服务端域名
  optional int32 type = 3; // 请求类型，1：获取业务类型appId，2：获取游戏的appId
}

// GameProxyService.java
// 获取App回调配置信息
// domain = 4302, op = 240
message RequestInvokeTarget {
  optional InvokeTargetParams param = 1; //参数
}

// rcode == 1 (ILLEGAL_PARAMS) = 参数非法
// rcode == 2 (NOT_EXISTS) = 映射不存在
// rcode == 3 (ERROR_TARGET) = 目标服务错误
// rcode == 4 (ERROR) = 内部错误
message ResponseInvokeTarget {
  required int32 bizCode = 1; // 业务错误码
  required string msg = 2; // 错误消息
  required string data = 3; // 数据
}

// GameProxyService.java
// 根据传入的appId、gameId、type 获取对应的appId、appSecret
// domain = 4302, op = 241
message RequestGetAppIdAppSecretByParam {
  optional GetAppIdAppSecretParam param = 1; //参数
}

// rcode == 1 (ILLEGAL_PARAMS) = 参数非法
// rcode == 2 (NOT_EXISTS) = 映射不存在
// rcode == 3 (ERROR) = 内部错误
message ResponseGetAppIdAppSecretByParam {
  optional string appId = 1;
  optional string appSecret = 2;
}